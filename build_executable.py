#!/usr/bin/env python3
"""
Build script to create standalone executable using PyInstaller
"""

import os
import sys
import subprocess

def build_executable():
    """Build standalone executable"""
    
    # Check if PyInstaller is installed
    try:
        import PyInstaller
    except ImportError:
        print("PyInstaller not found. Installing...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
    
    # PyInstaller command
    cmd = [
        "pyinstaller",
        "--onefile",  # Create single executable
        "--windowed",  # No console window
        "--name", "ExcelRecovery",
        "--icon", "icon.ico" if os.path.exists("icon.ico") else None,
        "--add-data", "src;src",  # Include source files
        "--hidden-import", "tkinter",
        "--hidden-import", "openpyxl",
        "main.py"
    ]
    
    # Remove None values
    cmd = [arg for arg in cmd if arg is not None]
    
    print("Building executable...")
    print(f"Command: {' '.join(cmd)}")
    
    try:
        subprocess.check_call(cmd)
        print("\nBuild completed successfully!")
        print("Executable created in 'dist' folder")
    except subprocess.CalledProcessError as e:
        print(f"Build failed: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = build_executable()
    if not success:
        sys.exit(1)
