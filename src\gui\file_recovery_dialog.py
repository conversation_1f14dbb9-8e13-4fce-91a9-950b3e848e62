"""
File Recovery Dialog
Handles the recovery process for selected Excel files
"""

import tkinter as tk
import tkinter.ttk as ttk
from tkinter import messagebox, filedialog
import os
import shutil
from datetime import datetime
from typing import List


class FileRecoveryDialog:
    """Dialog for recovering selected Excel files"""
    
    def __init__(self, parent, file_paths: List[str]):
        self.parent = parent
        self.file_paths = file_paths
        self.recovery_location = ""
        
        # Create dialog window
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("Recover Excel Files")
        self.dialog.geometry("600x500")
        self.dialog.resizable(True, True)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # Center the dialog
        self.center_dialog()
        
        # Setup GUI
        self.setup_gui()
        
    def center_dialog(self):
        """Center the dialog on the parent window"""
        self.dialog.update_idletasks()
        x = self.parent.winfo_x() + (self.parent.winfo_width() // 2) - (self.dialog.winfo_width() // 2)
        y = self.parent.winfo_y() + (self.parent.winfo_height() // 2) - (self.dialog.winfo_height() // 2)
        self.dialog.geometry(f"+{x}+{y}")
    
    def setup_gui(self):
        """Setup the dialog GUI"""
        main_frame = ttk.Frame(self.dialog, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Title
        title_label = ttk.Label(main_frame, text="File Recovery", 
                               font=('Arial', 14, 'bold'))
        title_label.pack(pady=(0, 10))
        
        # Files to recover frame
        files_frame = ttk.LabelFrame(main_frame, text="Files to Recover", padding="5")
        files_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # File list
        self.file_listbox = tk.Listbox(files_frame, height=8)
        file_scroll = ttk.Scrollbar(files_frame, orient=tk.VERTICAL, command=self.file_listbox.yview)
        self.file_listbox.configure(yscrollcommand=file_scroll.set)
        
        self.file_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        file_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Populate file list
        for file_path in self.file_paths:
            filename = os.path.basename(file_path)
            self.file_listbox.insert(tk.END, filename)
        
        # Recovery options frame
        options_frame = ttk.LabelFrame(main_frame, text="Recovery Options", padding="5")
        options_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Destination selection
        dest_frame = ttk.Frame(options_frame)
        dest_frame.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Label(dest_frame, text="Recovery Location:").pack(side=tk.LEFT)
        
        self.location_var = tk.StringVar(value="Select destination folder...")
        self.location_label = ttk.Label(dest_frame, textvariable=self.location_var, 
                                       relief=tk.SUNKEN, width=50)
        self.location_label.pack(side=tk.LEFT, padx=(5, 5), fill=tk.X, expand=True)
        
        self.browse_button = ttk.Button(dest_frame, text="Browse", 
                                       command=self.browse_destination)
        self.browse_button.pack(side=tk.RIGHT)
        
        # Naming options
        naming_frame = ttk.LabelFrame(options_frame, text="File Naming", padding="5")
        naming_frame.pack(fill=tk.X, pady=(5, 0))
        
        self.naming_var = tk.StringVar(value="original")
        
        ttk.Radiobutton(naming_frame, text="Keep original names", 
                       variable=self.naming_var, value="original").pack(anchor=tk.W)
        
        ttk.Radiobutton(naming_frame, text="Add timestamp prefix", 
                       variable=self.naming_var, value="timestamp").pack(anchor=tk.W)
        
        ttk.Radiobutton(naming_frame, text="Add 'Recovered_' prefix", 
                       variable=self.naming_var, value="recovered").pack(anchor=tk.W)
        
        # Additional options
        additional_frame = ttk.LabelFrame(options_frame, text="Additional Options", padding="5")
        additional_frame.pack(fill=tk.X, pady=(5, 0))
        
        self.overwrite_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(additional_frame, text="Overwrite existing files", 
                       variable=self.overwrite_var).pack(anchor=tk.W)
        
        self.open_folder_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(additional_frame, text="Open destination folder after recovery", 
                       variable=self.open_folder_var).pack(anchor=tk.W)
        
        # Progress frame
        progress_frame = ttk.Frame(main_frame)
        progress_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.progress_var = tk.StringVar(value="Ready to recover files")
        self.progress_label = ttk.Label(progress_frame, textvariable=self.progress_var)
        self.progress_label.pack()
        
        self.progress_bar = ttk.Progressbar(progress_frame, mode='determinate')
        self.progress_bar.pack(fill=tk.X, pady=(5, 0))
        
        # Buttons frame
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X)
        
        self.recover_button = ttk.Button(buttons_frame, text="Start Recovery", 
                                        command=self.start_recovery)
        self.recover_button.pack(side=tk.LEFT, padx=(0, 5))
        
        self.cancel_button = ttk.Button(buttons_frame, text="Cancel", 
                                       command=self.dialog.destroy)
        self.cancel_button.pack(side=tk.RIGHT)
    
    def browse_destination(self):
        """Browse for destination folder"""
        folder = filedialog.askdirectory(title="Select Recovery Destination")
        if folder:
            self.recovery_location = folder
            self.location_var.set(folder)
    
    def start_recovery(self):
        """Start the file recovery process"""
        if not self.recovery_location:
            messagebox.showerror("Error", "Please select a destination folder")
            return
        
        if not os.path.exists(self.recovery_location):
            messagebox.showerror("Error", "Selected destination folder does not exist")
            return
        
        # Disable buttons during recovery
        self.recover_button.config(state='disabled')
        self.browse_button.config(state='disabled')
        
        # Start recovery
        self.recover_files()
    
    def recover_files(self):
        """Recover the selected files"""
        total_files = len(self.file_paths)
        recovered_count = 0
        failed_files = []
        
        self.progress_bar.config(maximum=total_files)
        
        for i, file_path in enumerate(self.file_paths):
            try:
                # Update progress
                self.progress_var.set(f"Recovering {os.path.basename(file_path)}...")
                self.progress_bar.config(value=i)
                self.dialog.update()
                
                # Generate destination filename
                dest_filename = self.generate_filename(file_path)
                dest_path = os.path.join(self.recovery_location, dest_filename)
                
                # Check if file exists and handle overwrite
                if os.path.exists(dest_path) and not self.overwrite_var.get():
                    # Generate unique filename
                    base, ext = os.path.splitext(dest_filename)
                    counter = 1
                    while os.path.exists(dest_path):
                        dest_filename = f"{base}_{counter}{ext}"
                        dest_path = os.path.join(self.recovery_location, dest_filename)
                        counter += 1
                
                # Copy the file
                shutil.copy2(file_path, dest_path)
                recovered_count += 1
                
            except Exception as e:
                failed_files.append((os.path.basename(file_path), str(e)))
        
        # Update final progress
        self.progress_bar.config(value=total_files)
        self.progress_var.set(f"Recovery complete: {recovered_count}/{total_files} files")
        
        # Show results
        self.show_recovery_results(recovered_count, total_files, failed_files)
        
        # Open destination folder if requested
        if self.open_folder_var.get():
            try:
                os.startfile(self.recovery_location)
            except Exception:
                pass  # Ignore errors opening folder
        
        # Re-enable buttons
        self.recover_button.config(state='normal')
        self.browse_button.config(state='normal')
    
    def generate_filename(self, file_path: str) -> str:
        """Generate the destination filename based on naming options"""
        original_name = os.path.basename(file_path)
        name, ext = os.path.splitext(original_name)
        
        naming_option = self.naming_var.get()
        
        if naming_option == "timestamp":
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            return f"{timestamp}_{original_name}"
        elif naming_option == "recovered":
            return f"Recovered_{original_name}"
        else:  # original
            return original_name
    
    def show_recovery_results(self, recovered: int, total: int, failed: List):
        """Show recovery results"""
        if failed:
            error_msg = f"Recovery completed with errors:\n\n"
            error_msg += f"Successfully recovered: {recovered}/{total} files\n\n"
            error_msg += "Failed files:\n"
            for filename, error in failed:
                error_msg += f"- {filename}: {error}\n"
            messagebox.showerror("Recovery Results", error_msg)
        else:
            success_msg = f"Recovery completed successfully!\n\n"
            success_msg += f"Recovered {recovered} files to:\n{self.recovery_location}"
            messagebox.showinfo("Recovery Results", success_msg)
