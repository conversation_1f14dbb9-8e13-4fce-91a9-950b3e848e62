"""
File Discovery Module
Scans common Excel temporary file locations for recoverable files
"""

import os
import glob
import tempfile
from pathlib import Path
from typing import List, Dict, Optional
from datetime import datetime


class ExcelFileDiscovery:
    """Discovers Excel temporary and auto-recovery files"""
    
    def __init__(self):
        self.excel_extensions = ['.xlsx', '.xls', '.xlsm', '.xlsb', '.xltx', '.xltm']
        self.word_extensions = ['.docx', '.doc', '.dotx', '.dotm']
        self.recovery_extensions = ['.asd', '.wbk']  # Auto-recovery and backup files
        self.temp_extensions = ['.tmp', '.xlk']
        
    def get_standard_locations(self) -> List[str]:
        """Get standard Excel temporary file locations"""
        username = os.getenv('USERNAME', 'User')
        locations = []
        
        # Office UnsavedFiles location
        unsaved_path = os.path.expandvars(r'%LOCALAPPDATA%\Microsoft\Office\UnsavedFiles')
        if os.path.exists(unsaved_path):
            locations.append(unsaved_path)
            
        # Excel auto-recovery location
        excel_path = os.path.expandvars(r'%APPDATA%\Microsoft\Excel')
        if os.path.exists(excel_path):
            locations.append(excel_path)
            
        # Windows temp directory
        temp_path = tempfile.gettempdir()
        locations.append(temp_path)
        
        # User's Documents folder (sometimes Excel saves temp files here)
        documents_path = os.path.expanduser('~/Documents')
        if os.path.exists(documents_path):
            locations.append(documents_path)
            
        return locations
    
    def scan_location(self, location: str) -> List[Dict]:
        """Scan a specific location for Excel files"""
        found_files = []
        
        if not os.path.exists(location):
            return found_files
            
        try:
            # Search for unsaved/recovery files only
            unsaved_patterns = [
                '*.asd',  # Auto-recovery files (Excel/Word)
                '*.wbk',  # Word backup files
                'AutoRecovery save of *.asd',  # Auto-recovery files
                'Backup of *.wbk',  # Backup files
            ]

            # Search in UnsavedFiles directory for actual unsaved files
            if 'UnsavedFiles' in location:
                for ext in self.excel_extensions + self.word_extensions:
                    pattern = os.path.join(location, f'*{ext}')
                    for file_path in glob.glob(pattern):
                        if os.path.isfile(file_path):
                            file_info = self._get_file_info(file_path)
                            if file_info and self._is_likely_unsaved(file_path):
                                found_files.append(file_info)

            # Search for auto-recovery and backup files
            for pattern in unsaved_patterns:
                full_pattern = os.path.join(location, pattern)
                for file_path in glob.glob(full_pattern):
                    if os.path.isfile(file_path):
                        file_info = self._get_file_info(file_path)
                        if file_info and file_info not in found_files:
                            found_files.append(file_info)
                            
        except PermissionError:
            # Skip directories we don't have permission to access
            pass
        except Exception as e:
            print(f"Error scanning {location}: {e}")
            
        return found_files
    
    def _get_file_info(self, file_path: str) -> Optional[Dict]:
        """Get information about a file"""
        try:
            stat = os.stat(file_path)
            return {
                'path': file_path,
                'name': os.path.basename(file_path),
                'size': stat.st_size,
                'modified': datetime.fromtimestamp(stat.st_mtime),
                'created': datetime.fromtimestamp(stat.st_ctime),
                'extension': os.path.splitext(file_path)[1].lower(),
                'is_temp': self._is_temp_file(file_path),
                'is_auto_recovery': self._is_auto_recovery_file(file_path),
                'is_unsaved': self._is_likely_unsaved(file_path),
                'file_type': self._get_file_type(file_path)
            }
        except (OSError, PermissionError):
            return None
    
    def _is_temp_file(self, file_path: str) -> bool:
        """Check if file appears to be a temporary file"""
        name = os.path.basename(file_path).lower()
        return (
            name.startswith('~$') or
            name.startswith('excel_') or
            '.tmp' in name or
            name.endswith('.tmp')
        )
    
    def _is_auto_recovery_file(self, file_path: str) -> bool:
        """Check if file appears to be an auto-recovery file"""
        name = os.path.basename(file_path).lower()
        return (name.endswith('.asd') or
                name.endswith('.wbk') or
                'autorecovery' in name.lower() or
                'backup of' in name.lower())

    def _is_likely_unsaved(self, file_path: str) -> bool:
        """Check if file is likely an unsaved document"""
        name = os.path.basename(file_path).lower()

        # Files in UnsavedFiles directory are likely unsaved
        if 'unsavedfiles' in file_path.lower():
            return True

        # Check for typical unsaved file patterns
        unsaved_indicators = [
            'document',  # Default Word document names
            'workbook',  # Default Excel workbook names
            'book',      # Excel book names
            'sheet',     # Excel sheet names
        ]

        # Check if filename contains unsaved indicators and numbers
        for indicator in unsaved_indicators:
            if indicator in name and any(char.isdigit() for char in name):
                return True

        return False

    def _get_file_type(self, file_path: str) -> str:
        """Determine the file type for display"""
        ext = os.path.splitext(file_path)[1].lower()

        if self._is_auto_recovery_file(file_path):
            if ext in self.excel_extensions or '.xl' in ext:
                return "Excel Auto-Recovery"
            elif ext in self.word_extensions or '.doc' in ext:
                return "Word Auto-Recovery"
            else:
                return "Auto-Recovery"
        elif self._is_likely_unsaved(file_path):
            if ext in self.excel_extensions:
                return "Unsaved Excel"
            elif ext in self.word_extensions:
                return "Unsaved Word"
            else:
                return "Unsaved Document"
        elif ext in self.excel_extensions:
            return "Excel File"
        elif ext in self.word_extensions:
            return "Word File"
        else:
            return "Office Document"
    
    def discover_all_files(self, additional_locations: List[str] = None) -> List[Dict]:
        """Discover all Excel files from standard and additional locations"""
        all_files = []
        locations = self.get_standard_locations()
        
        if additional_locations:
            locations.extend(additional_locations)
        
        for location in locations:
            files = self.scan_location(location)
            all_files.extend(files)
        
        # Remove duplicates based on file path
        seen_paths = set()
        unique_files = []
        for file_info in all_files:
            if file_info['path'] not in seen_paths:
                seen_paths.add(file_info['path'])
                unique_files.append(file_info)
        
        # Sort by modification time (newest first)
        unique_files.sort(key=lambda x: x['modified'], reverse=True)
        
        return unique_files
