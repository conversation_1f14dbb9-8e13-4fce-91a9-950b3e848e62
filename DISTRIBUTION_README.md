# Excel File Recovery Tool - Distribution Package

## Quick Start

### Option 1: Run the Executable (Recommended)
1. Navigate to the `dist` folder
2. Double-click `ExcelRecovery.exe`
3. The application will launch immediately

### Option 2: Use the Batch File
1. Double-click `run_excel_recovery.bat` in the main folder
2. This will launch the executable and show a confirmation message

### Option 3: Run from Source (Requires Python)
1. Ensure Python 3.7+ is installed
2. Install dependencies: `pip install -r requirements.txt`
3. Run: `python main.py`

## What's Included

### Executable
- **ExcelRecovery.exe** (10.3 MB) - Standalone executable that includes all dependencies
- No Python installation required
- No additional dependencies needed
- Works on Windows 7 and later

### Source Code
- Complete Python source code in the `src` folder
- Test suite in the `tests` folder
- Build scripts and configuration files

### Documentation
- **docs/USER_GUIDE.md** - Comprehensive user guide
- **README.md** - Project overview and setup instructions
- **CHANGELOG.md** - Version history and features

## System Requirements

### For Executable
- Windows 7 or later (64-bit recommended)
- 50 MB free disk space
- No additional software required

### For Source Code
- Python 3.7 or later
- Dependencies listed in requirements.txt
- Windows, macOS, or Linux

## Features

✅ **Automatic Scanning** - Finds Excel files in common temporary locations  
✅ **Smart Analysis** - Determines file recoverability and corruption level  
✅ **Advanced Filtering** - Search and filter by name, type, and date  
✅ **Batch Recovery** - Recover multiple files at once  
✅ **Safe Recovery** - Multiple naming options to prevent overwrites  
✅ **User-Friendly GUI** - Clean, intuitive interface  
✅ **Progress Tracking** - Real-time progress for all operations  

## Supported File Types

- Excel Workbooks (.xlsx, .xlsm, .xlsb)
- Excel 97-2003 Workbooks (.xls)
- Excel Templates (.xltx, .xltm)
- Auto-recovery files (.asd)
- Excel temporary files (.tmp, .xlk)

## Common Locations Scanned

- `%LOCALAPPDATA%\Microsoft\Office\UnsavedFiles`
- `%APPDATA%\Microsoft\Excel`
- `%TEMP%` (Windows temporary directory)
- Documents folder
- Custom locations you specify

## Troubleshooting

### Executable Won't Start
- Ensure you're running on Windows 7 or later
- Try running as administrator
- Check Windows Defender/antivirus isn't blocking it

### No Files Found
- Ensure Excel was recently used on this computer
- Try adding custom locations where you worked
- Check if Excel's auto-recovery is enabled

### Recovery Issues
- Ensure destination folder exists and is writable
- Check available disk space
- Close Excel and other Office applications

## Security Note

This software only reads from temporary file locations and copies files to your chosen destination. It does not:
- Modify system files
- Access personal data outside Excel temp locations
- Connect to the internet
- Install additional software

## Support

For detailed usage instructions, see `docs/USER_GUIDE.md`

## Version Information

- **Version**: 1.0.0
- **Build Date**: December 21, 2024
- **Python Version**: 3.13.3
- **PyInstaller Version**: 6.14.1
