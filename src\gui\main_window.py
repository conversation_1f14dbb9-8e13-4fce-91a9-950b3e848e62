"""
Main Window GUI for Excel File Recovery Software
"""

import tkinter as tk
import tkinter.ttk as ttk
from tkinter import messagebox, filedialog
import threading
from datetime import datetime
from typing import List, Dict

from core.file_discovery import ExcelFileDiscovery
from core.file_analysis import ExcelFileAnalyzer
from gui.file_recovery_dialog import FileRecoveryDialog


class ExcelRecoveryApp:
    """Main application window"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Excel File Recovery Tool")
        self.root.geometry("1000x700")
        self.root.minsize(800, 600)
        
        # Initialize core components
        self.file_discovery = ExcelFileDiscovery()
        self.file_analyzer = ExcelFileAnalyzer()
        
        # Data storage
        self.discovered_files = []
        self.selected_files = []
        
        # Setup GUI
        self.setup_gui()
        self.setup_menu()

        # Initialize empty file list
        self.status_var.set("Ready - Click 'Scan for Files' to begin")
    
    def setup_gui(self):
        """Setup the main GUI components"""
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(1, weight=1)
        
        # Title
        title_label = ttk.Label(main_frame, text="Excel File Recovery Tool", 
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 10))
        
        # Control buttons frame
        controls_frame = ttk.Frame(main_frame)
        controls_frame.grid(row=1, column=0, sticky=(tk.W, tk.N), padx=(0, 10))
        
        # Scan button
        self.scan_button = ttk.Button(controls_frame, text="Scan for Files", 
                                     command=self.scan_files)
        self.scan_button.pack(pady=(0, 5), fill=tk.X)
        
        # Add location button
        self.add_location_button = ttk.Button(controls_frame, text="Add Custom Location",
                                            command=self.add_custom_location)
        self.add_location_button.pack(pady=(0, 5), fill=tk.X)

        # Filter frame
        filter_frame = ttk.LabelFrame(controls_frame, text="Filters", padding="5")
        filter_frame.pack(pady=(0, 5), fill=tk.X)

        # Search entry
        ttk.Label(filter_frame, text="Search:").pack(anchor=tk.W)
        self.search_var = tk.StringVar()
        self.search_var.trace_add('write', self.on_search_change)
        search_entry = ttk.Entry(filter_frame, textvariable=self.search_var)
        search_entry.pack(fill=tk.X, pady=(0, 5))

        # File type filter
        ttk.Label(filter_frame, text="File Type:").pack(anchor=tk.W)
        self.filter_var = tk.StringVar(value="Unsaved Only")
        filter_combo = ttk.Combobox(filter_frame, textvariable=self.filter_var,
                                   values=["All", "Unsaved Only", "Unsaved Excel", "Unsaved Word", "Auto-Recovery"],
                                   state="readonly")
        filter_combo.pack(fill=tk.X, pady=(0, 5))
        filter_combo.bind('<<ComboboxSelected>>', self.on_filter_change)

        # Date filter
        ttk.Label(filter_frame, text="Modified:").pack(anchor=tk.W)
        self.date_filter_var = tk.StringVar(value="All")
        date_combo = ttk.Combobox(filter_frame, textvariable=self.date_filter_var,
                                 values=["All", "Today", "Yesterday", "Last Week", "Last Month"],
                                 state="readonly")
        date_combo.pack(fill=tk.X)
        date_combo.bind('<<ComboboxSelected>>', self.on_filter_change)
        
        # Analyze selected button
        self.analyze_button = ttk.Button(controls_frame, text="Analyze Selected", 
                                       command=self.analyze_selected_files)
        self.analyze_button.pack(pady=(0, 5), fill=tk.X)
        
        # Recover selected button
        self.recover_button = ttk.Button(controls_frame, text="Recover Selected", 
                                       command=self.recover_selected_files)
        self.recover_button.pack(pady=(0, 10), fill=tk.X)
        
        # Progress bar
        self.progress_var = tk.StringVar(value="Ready")
        progress_label = ttk.Label(controls_frame, textvariable=self.progress_var)
        progress_label.pack(pady=(0, 5))
        
        self.progress_bar = ttk.Progressbar(controls_frame, mode='indeterminate')
        self.progress_bar.pack(fill=tk.X, pady=(0, 10))
        
        # File list frame
        list_frame = ttk.LabelFrame(main_frame, text="Discovered Files", padding="5")
        list_frame.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))
        list_frame.columnconfigure(0, weight=1)
        list_frame.rowconfigure(0, weight=1)
        
        # File treeview
        columns = ('Name', 'Location', 'Size', 'Modified', 'Type', 'Status')
        self.file_tree = ttk.Treeview(list_frame, columns=columns, show='headings', 
                                     selectmode='extended')
        
        # Configure columns
        self.file_tree.heading('Name', text='File Name')
        self.file_tree.heading('Location', text='Location')
        self.file_tree.heading('Size', text='Size')
        self.file_tree.heading('Modified', text='Last Modified')
        self.file_tree.heading('Type', text='Type')
        self.file_tree.heading('Status', text='Status')
        
        self.file_tree.column('Name', width=200)
        self.file_tree.column('Location', width=300)
        self.file_tree.column('Size', width=80)
        self.file_tree.column('Modified', width=120)
        self.file_tree.column('Type', width=100)
        self.file_tree.column('Status', width=100)
        
        # Scrollbars for treeview
        tree_scroll_y = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.file_tree.yview)
        tree_scroll_x = ttk.Scrollbar(list_frame, orient=tk.HORIZONTAL, command=self.file_tree.xview)
        self.file_tree.configure(yscrollcommand=tree_scroll_y.set, xscrollcommand=tree_scroll_x.set)
        
        self.file_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        tree_scroll_y.grid(row=0, column=1, sticky=(tk.N, tk.S))
        tree_scroll_x.grid(row=1, column=0, sticky=(tk.W, tk.E))
        
        # Details frame
        details_frame = ttk.LabelFrame(main_frame, text="File Details", padding="5")
        details_frame.grid(row=1, column=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        details_frame.columnconfigure(0, weight=1)
        
        # Details text widget
        self.details_text = tk.Text(details_frame, width=30, height=20, wrap=tk.WORD)
        details_scroll = ttk.Scrollbar(details_frame, orient=tk.VERTICAL, command=self.details_text.yview)
        self.details_text.configure(yscrollcommand=details_scroll.set)
        
        self.details_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        details_scroll.grid(row=0, column=1, sticky=(tk.N, tk.S))
        details_frame.rowconfigure(0, weight=1)
        
        # Status bar
        self.status_var = tk.StringVar(value="Ready")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))
        
        # Bind events
        self.file_tree.bind('<<TreeviewSelect>>', self.on_file_select)
        self.file_tree.bind('<Double-1>', self.on_file_double_click)
    
    def setup_menu(self):
        """Setup the application menu"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # File menu
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="File", menu=file_menu)
        file_menu.add_command(label="Scan for Files", command=self.scan_files)
        file_menu.add_command(label="Add Custom Location", command=self.add_custom_location)
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.root.quit)
        
        # Tools menu
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Tools", menu=tools_menu)
        tools_menu.add_command(label="Analyze All Files", command=self.analyze_all_files)
        tools_menu.add_command(label="Clear Results", command=self.clear_results)
        
        # Help menu
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Help", menu=help_menu)
        help_menu.add_command(label="About", command=self.show_about)
    
    def scan_files(self):
        """Scan for Excel files in a separate thread"""
        def scan_worker():
            self.progress_var.set("Scanning for files...")
            self.progress_bar.start()
            self.scan_button.config(state='disabled')
            
            try:
                self.discovered_files = self.file_discovery.discover_all_files()
                self.root.after(0, self.update_file_list)
                self.status_var.set(f"Found {len(self.discovered_files)} files")
            except Exception as e:
                self.root.after(0, lambda: messagebox.showerror("Error", f"Scan failed: {str(e)}"))
            finally:
                self.progress_bar.stop()
                self.progress_var.set("Ready")
                self.scan_button.config(state='normal')
        
        threading.Thread(target=scan_worker, daemon=True).start()
    
    def update_file_list(self):
        """Update the file list display"""
        # Apply filters instead of showing all files
        self.apply_filters()
    
    def format_file_size(self, size_bytes: int) -> str:
        """Format file size in human readable format"""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size_bytes < 1024.0:
                return f"{size_bytes:.1f} {unit}"
            size_bytes /= 1024.0
        return f"{size_bytes:.1f} TB"
    
    def on_file_select(self, event):
        """Handle file selection in the tree"""
        selection = self.file_tree.selection()
        if selection:
            item = selection[0]
            file_path = self.file_tree.item(item)['values'][1]
            self.show_file_details(file_path)
    
    def show_file_details(self, file_path: str):
        """Show details for the selected file"""
        # Find file info
        file_info = None
        for info in self.discovered_files:
            if info['path'] == file_path:
                file_info = info
                break
        
        if not file_info:
            return
        
        # Display details
        details = f"File: {file_info['name']}\n"
        details += f"Path: {file_info['path']}\n"
        details += f"Size: {self.format_file_size(file_info['size'])}\n"
        details += f"Modified: {file_info['modified']}\n"
        details += f"Created: {file_info['created']}\n"
        details += f"Extension: {file_info['extension']}\n"
        details += f"Type: {file_info.get('file_type', 'Unknown')}\n"
        details += f"Is Unsaved: {file_info.get('is_unsaved', False)}\n"
        details += f"Is Auto-Recovery: {file_info.get('is_auto_recovery', False)}\n"
        
        self.details_text.delete(1.0, tk.END)
        self.details_text.insert(1.0, details)
    
    def on_file_double_click(self, event):
        """Handle double-click on file"""
        selection = self.file_tree.selection()
        if selection:
            item = selection[0]
            file_path = self.file_tree.item(item)['values'][1]
            self.analyze_single_file(file_path)
    
    def analyze_single_file(self, file_path: str):
        """Analyze a single file"""
        def analyze_worker():
            try:
                analysis = self.file_analyzer.analyze_file(file_path)
                self.root.after(0, lambda: self.show_analysis_result(file_path, analysis))
            except Exception as e:
                self.root.after(0, lambda: messagebox.showerror("Error", f"Analysis failed: {str(e)}"))
        
        threading.Thread(target=analyze_worker, daemon=True).start()
    
    def show_analysis_result(self, file_path: str, analysis: Dict):
        """Show analysis result"""
        # Update tree item status
        for item in self.file_tree.get_children():
            if self.file_tree.item(item)['values'][1] == file_path:
                values = list(self.file_tree.item(item)['values'])
                values[5] = "Recoverable" if analysis['is_recoverable'] else "Not Recoverable"
                self.file_tree.item(item, values=values)
                break
        
        # Show detailed analysis
        details = f"Analysis Results for: {analysis['path']}\n\n"
        details += f"File Type: {analysis['file_type']}\n"
        details += f"Valid Excel: {analysis['is_valid_excel']}\n"
        details += f"Recoverable: {analysis['is_recoverable']}\n"
        details += f"Corruption Level: {analysis['corruption_level']}\n"
        
        if analysis['estimated_content']:
            details += f"Content: {analysis['estimated_content']}\n"
        
        if analysis['analysis_errors']:
            details += f"\nErrors:\n"
            for error in analysis['analysis_errors']:
                details += f"- {error}\n"
        
        recommendation = self.file_analyzer.get_recovery_recommendation(analysis)
        details += f"\nRecommendation: {recommendation}\n"
        
        self.details_text.delete(1.0, tk.END)
        self.details_text.insert(1.0, details)
    
    def analyze_selected_files(self):
        """Analyze selected files"""
        selection = self.file_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select files to analyze")
            return
        
        for item in selection:
            file_path = self.file_tree.item(item)['values'][1]
            self.analyze_single_file(file_path)
    
    def analyze_all_files(self):
        """Analyze all discovered files"""
        if not self.discovered_files:
            messagebox.showwarning("Warning", "No files to analyze. Please scan first.")
            return
        
        for file_info in self.discovered_files:
            self.analyze_single_file(file_info['path'])
    
    def recover_selected_files(self):
        """Recover selected files"""
        selection = self.file_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select files to recover")
            return
        
        selected_files = []
        for item in selection:
            file_path = self.file_tree.item(item)['values'][1]
            selected_files.append(file_path)
        
        # Open recovery dialog
        dialog = FileRecoveryDialog(self.root, selected_files)
        self.root.wait_window(dialog.dialog)
    
    def add_custom_location(self):
        """Add a custom location to scan"""
        location = filedialog.askdirectory(title="Select folder to scan for Excel files")
        if location:
            # Add to discovery and rescan
            additional_files = self.file_discovery.scan_location(location)
            self.discovered_files.extend(additional_files)
            self.update_file_list()
            self.status_var.set(f"Added {len(additional_files)} files from custom location")
    
    def clear_results(self):
        """Clear all results"""
        self.discovered_files.clear()
        self.update_file_list()
        self.details_text.delete(1.0, tk.END)
        self.status_var.set("Results cleared")
    
    def show_about(self):
        """Show about dialog"""
        about_text = """Excel File Recovery Tool v1.0

A GUI-based application to recover unsaved Excel files 
from temporary locations and auto-recovery folders.

Features:
- Scan common Excel temporary file locations
- Analyze file recoverability
- Batch recovery capabilities
- User-friendly interface

© 2024 Excel Recovery Software"""
        
        messagebox.showinfo("About", about_text)
    
    def on_search_change(self, *args):
        """Handle search text change"""
        self.apply_filters()

    def on_filter_change(self, event=None):
        """Handle filter change"""
        self.apply_filters()

    def apply_filters(self):
        """Apply current filters to the file list"""
        if not hasattr(self, 'discovered_files') or not self.discovered_files:
            return

        search_text = self.search_var.get().lower()
        file_type_filter = self.filter_var.get()
        date_filter = self.date_filter_var.get()

        # Clear existing items
        for item in self.file_tree.get_children():
            self.file_tree.delete(item)

        # Filter files
        filtered_files = []
        for file_info in self.discovered_files:
            # Apply search filter
            if search_text and search_text not in file_info['name'].lower():
                continue

            # Apply file type filter
            if file_type_filter != "All":
                file_type = file_info.get('file_type', '')
                if file_type_filter == "Unsaved Only" and not (file_info.get('is_unsaved', False) or file_info.get('is_auto_recovery', False)):
                    continue
                elif file_type_filter == "Unsaved Excel" and 'Excel' not in file_type:
                    continue
                elif file_type_filter == "Unsaved Word" and 'Word' not in file_type:
                    continue
                elif file_type_filter == "Auto-Recovery" and not file_info.get('is_auto_recovery', False):
                    continue

            # Apply date filter
            if date_filter != "All":
                from datetime import datetime, timedelta
                now = datetime.now()
                file_date = file_info['modified']

                if date_filter == "Today" and file_date.date() != now.date():
                    continue
                elif date_filter == "Yesterday" and file_date.date() != (now - timedelta(days=1)).date():
                    continue
                elif date_filter == "Last Week" and file_date < (now - timedelta(weeks=1)):
                    continue
                elif date_filter == "Last Month" and file_date < (now - timedelta(days=30)):
                    continue

            filtered_files.append(file_info)

        # Add filtered files to tree
        for file_info in filtered_files:
            size_str = self.format_file_size(file_info['size'])
            modified_str = file_info['modified'].strftime('%Y-%m-%d %H:%M')

            file_type = file_info.get('file_type', 'Unknown')

            self.file_tree.insert('', 'end', values=(
                file_info['name'],
                file_info['path'],
                size_str,
                modified_str,
                file_type,
                "Not Analyzed"
            ))

        # Update status
        total_files = len(self.discovered_files)
        filtered_count = len(filtered_files)
        if filtered_count != total_files:
            self.status_var.set(f"Showing {filtered_count} of {total_files} files")
        else:
            self.status_var.set(f"Found {total_files} files")

    def run(self):
        """Run the application"""
        self.root.mainloop()
