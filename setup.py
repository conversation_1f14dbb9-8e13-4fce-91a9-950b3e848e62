#!/usr/bin/env python3
"""
Setup script for Excel File Recovery Software
"""

from setuptools import setup, find_packages
import os

# Read README file
with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

# Read requirements
with open("requirements.txt", "r", encoding="utf-8") as fh:
    requirements = [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="excel-file-recovery",
    version="1.0.0",
    author="Excel Recovery Software",
    author_email="<EMAIL>",
    description="A GUI-based application to recover unsaved Excel files",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/username/excel-file-recovery",
    packages=find_packages(where="src"),
    package_dir={"": "src"},
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: End Users/Desktop",
        "License :: OSI Approved :: MIT License",
        "Operating System :: Microsoft :: Windows",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.7",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Office/Business",
        "Topic :: System :: Recovery Tools",
        "Topic :: Utilities",
    ],
    python_requires=">=3.7",
    install_requires=requirements,
    entry_points={
        "console_scripts": [
            "excel-recovery=main:main",
        ],
    },
    include_package_data=True,
    zip_safe=False,
)
