# Excel File Recovery Software - User Guide

## Overview

The Excel File Recovery Software is a GUI-based application designed to help you recover unsaved Excel files from temporary locations and auto-recovery folders on your Windows system.

## Getting Started

### Installation

1. Ensure you have Python 3.7 or later installed
2. Download or clone the software to your computer
3. Open a command prompt in the software directory
4. Install dependencies: `pip install -r requirements.txt`
5. Run the application: `python main.py`

### First Launch

When you first launch the application, it will automatically scan common Excel temporary file locations:
- `%LOCALAPPDATA%\Microsoft\Office\UnsavedFiles`
- `%APPDATA%\Microsoft\Excel`
- `%TEMP%` (Windows temporary directory)
- Your Documents folder

## Main Interface

### File List

The main window displays discovered files in a table with the following columns:
- **File Name**: The name of the discovered file
- **Location**: Full path to the file
- **Size**: File size in human-readable format
- **Last Modified**: When the file was last modified
- **Type**: File type (Excel File, Temp File, or Auto-Recovery)
- **Status**: Analysis status (Not Analyzed, Recoverable, Not Recoverable)

### Control Panel

The left panel contains several controls:

#### Scan for Files
- Click to rescan all standard locations for Excel files
- Use this if you've recently worked on Excel files

#### Add Custom Location
- Add additional folders to scan for Excel files
- Useful if you know Excel might have saved temporary files elsewhere

#### Filters
- **Search**: Type to filter files by name
- **File Type**: Filter by Excel Files, Temp Files, or Auto-Recovery files
- **Modified**: Filter by modification date (Today, Yesterday, Last Week, Last Month)

#### Analysis and Recovery
- **Analyze Selected**: Analyze selected files to determine recoverability
- **Recover Selected**: Start the recovery process for selected files

### File Details Panel

The right panel shows detailed information about the selected file:
- File properties (size, dates, type)
- Analysis results (if analyzed)
- Recovery recommendations

## Using the Software

### Step 1: Scan for Files

1. Launch the application (it will auto-scan on startup)
2. Or click "Scan for Files" to refresh the list
3. Add custom locations if needed using "Add Custom Location"

### Step 2: Filter and Select Files

1. Use the search box to find specific files by name
2. Use the file type filter to focus on specific types
3. Use the date filter to find recently modified files
4. Select files you want to recover (use Ctrl+click for multiple selection)

### Step 3: Analyze Files

1. Select the files you're interested in
2. Click "Analyze Selected" or double-click a file
3. Review the analysis results in the details panel
4. Look for files marked as "Recoverable"

### Step 4: Recover Files

1. Select the files you want to recover
2. Click "Recover Selected"
3. In the recovery dialog:
   - Choose a destination folder
   - Select naming options:
     - Keep original names
     - Add timestamp prefix
     - Add "Recovered_" prefix
   - Choose whether to overwrite existing files
   - Choose whether to open the destination folder after recovery
4. Click "Start Recovery"

## File Types Explained

### Excel Files
Regular Excel files (.xlsx, .xls, .xlsm, etc.) that may have been left in temporary locations.

### Temp Files
Temporary files created by Excel during editing:
- Files starting with `~$` (Excel lock files)
- Files with `.tmp` extension
- Files with Excel-specific temporary naming patterns

### Auto-Recovery Files
Files with `.asd` extension created by Excel's auto-recovery feature. These often contain unsaved work from crashed or improperly closed Excel sessions.

## Analysis Results

When you analyze a file, the software provides:

### File Type Detection
- Identifies the specific Excel format
- Validates file signatures

### Recoverability Assessment
- **Recoverable**: File appears intact and can likely be opened
- **Not Recoverable**: File is severely corrupted or not an Excel file

### Corruption Level
- **None or Minor**: File should open normally
- **Moderate**: File may open with some data loss
- **Severe**: File is heavily corrupted

### Recovery Recommendations
- **Highly recommended**: File appears intact
- **Recommended**: File may be partially recoverable
- **Cautiously recommended**: Recovery success uncertain
- **Not recommended**: File appears severely corrupted

## Tips for Best Results

1. **Run immediately after data loss**: The sooner you run the recovery, the better your chances
2. **Don't save new Excel files**: Avoid using Excel until recovery is complete to prevent overwriting temporary files
3. **Check multiple locations**: Use "Add Custom Location" to scan additional folders
4. **Analyze before recovering**: Always analyze files first to focus on recoverable ones
5. **Use descriptive naming**: Choose timestamp or "Recovered_" prefix to avoid confusion
6. **Backup recovered files**: Make copies of successfully recovered files immediately

## Troubleshooting

### No Files Found
- Ensure Excel was recently used on this computer
- Try adding custom locations where you might have worked
- Check if auto-recovery is enabled in Excel (File > Options > Save)

### Analysis Fails
- File may be locked by another process
- Try closing Excel and other Office applications
- Run the software as administrator if permission errors occur

### Recovery Fails
- Ensure destination folder exists and is writable
- Check available disk space
- Verify you have permission to write to the destination

### Files Won't Open After Recovery
- Try opening with Excel's built-in repair feature
- Some corruption may still exist in partially recovered files
- Consider using Excel's "Open and Repair" option

## Limitations

- Primarily designed for Windows systems
- Requires Excel 2010 or later for auto-recovery files
- Cannot recover files that have been permanently deleted
- Success depends on Excel's temporary file creation behavior
- Some heavily corrupted files may not be recoverable

## Support

If you encounter issues:
1. Check this user guide for troubleshooting tips
2. Ensure you're using the latest version of the software
3. Verify your Python installation and dependencies
4. Try running as administrator for permission issues
