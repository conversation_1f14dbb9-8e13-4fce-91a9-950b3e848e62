"""
Test cases for file analysis module
"""

import unittest
import tempfile
import os
import sys
import zipfile

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from core.file_analysis import ExcelFileAnalyzer


class TestFileAnalysis(unittest.TestCase):
    """Test cases for ExcelFileAnalyzer class"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.analyzer = ExcelFileAnalyzer()
        self.temp_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        """Clean up test fixtures"""
        import shutil
        shutil.rmtree(self.temp_dir)
    
    def create_test_file(self, filename, content=b'test content'):
        """Create a test file with given content"""
        file_path = os.path.join(self.temp_dir, filename)
        with open(file_path, 'wb') as f:
            f.write(content)
        return file_path
    
    def create_mock_xlsx_file(self, filename):
        """Create a mock XLSX file (ZIP format)"""
        file_path = os.path.join(self.temp_dir, filename)
        
        with zipfile.ZipFile(file_path, 'w') as zf:
            # Add minimal XLSX structure
            zf.writestr('[Content_Types].xml', '<?xml version="1.0"?><Types/>')
            zf.writestr('_rels/.rels', '<?xml version="1.0"?><Relationships/>')
            zf.writestr('xl/workbook.xml', '<?xml version="1.0"?><workbook/>')
            zf.writestr('xl/worksheets/sheet1.xml', '<?xml version="1.0"?><worksheet/>')
        
        return file_path
    
    def test_analyze_nonexistent_file(self):
        """Test analyzing a non-existent file"""
        analysis = self.analyzer.analyze_file('/nonexistent/file.xlsx')
        
        self.assertFalse(analysis['is_valid_excel'])
        self.assertFalse(analysis['is_recoverable'])
        self.assertIn('File does not exist', analysis['analysis_errors'])
    
    def test_analyze_empty_file(self):
        """Test analyzing an empty file"""
        file_path = self.create_test_file('empty.xlsx', b'')
        analysis = self.analyzer.analyze_file(file_path)
        
        self.assertFalse(analysis['is_valid_excel'])
        self.assertFalse(analysis['is_recoverable'])
        self.assertIn('File is empty', analysis['analysis_errors'])
    
    def test_check_file_signature_xlsx(self):
        """Test XLSX file signature detection"""
        # Create file with XLSX signature
        xlsx_content = b'PK\x03\x04' + b'\x00' * 100
        file_path = self.create_test_file('test.xlsx', xlsx_content)
        
        file_type, is_valid = self.analyzer._check_file_signature(file_path)
        self.assertTrue(is_valid)
        self.assertIn('xlsx', file_type.lower())
    
    def test_check_file_signature_xls(self):
        """Test XLS file signature detection"""
        # Create file with XLS signature
        xls_content = b'\xd0\xcf\x11\xe0\xa1\xb1\x1a\xe1' + b'\x00' * 100
        file_path = self.create_test_file('test.xls', xls_content)
        
        file_type, is_valid = self.analyzer._check_file_signature(file_path)
        self.assertTrue(is_valid)
        self.assertIn('xls', file_type.lower())
    
    def test_check_file_signature_unknown(self):
        """Test unknown file signature"""
        file_path = self.create_test_file('test.txt', b'regular text content')
        
        file_type, is_valid = self.analyzer._check_file_signature(file_path)
        self.assertFalse(is_valid)
        self.assertEqual(file_type, 'Unknown')
    
    def test_analyze_valid_xlsx_file(self):
        """Test analyzing a valid XLSX file"""
        file_path = self.create_mock_xlsx_file('valid.xlsx')
        analysis = self.analyzer.analyze_file(file_path)
        
        self.assertTrue(analysis['is_valid_excel'])
        self.assertTrue(analysis['is_recoverable'])
        self.assertIn('xlsx', analysis['file_type'].lower())
        self.assertIn('None or Minor', analysis['corruption_level'])
    
    def test_analyze_corrupted_zip(self):
        """Test analyzing a corrupted ZIP file"""
        # Create file with XLSX signature but invalid ZIP content
        corrupted_content = b'PK\x03\x04' + b'corrupted data'
        file_path = self.create_test_file('corrupted.xlsx', corrupted_content)
        
        analysis = self.analyzer.analyze_file(file_path)
        
        self.assertTrue(analysis['is_valid_excel'])  # Has valid signature
        self.assertFalse(analysis['is_recoverable'])  # But corrupted
        self.assertIn('not a valid ZIP archive', ' '.join(analysis['analysis_errors']))
    
    def test_analyze_potential_excel_file(self):
        """Test analyzing a file that might be a corrupted Excel file"""
        # Create file with Excel indicators but no valid signature
        content = b'some data Microsoft Excel xl/worksheets more data'
        file_path = self.create_test_file('potential.dat', content)
        
        analysis = self.analyzer.analyze_file(file_path)
        
        self.assertFalse(analysis['is_valid_excel'])
        # Should detect as potential Excel file due to indicators
        if analysis['is_recoverable']:
            self.assertIn('Potential Excel file', analysis['estimated_content'])
    
    def test_get_recovery_recommendation(self):
        """Test recovery recommendations"""
        # Test different scenarios
        analysis_good = {'is_recoverable': True, 'corruption_level': 'None or Minor'}
        analysis_moderate = {'is_recoverable': True, 'corruption_level': 'Moderate'}
        analysis_bad = {'is_recoverable': False, 'corruption_level': 'Severe'}
        
        rec_good = self.analyzer.get_recovery_recommendation(analysis_good)
        rec_moderate = self.analyzer.get_recovery_recommendation(analysis_moderate)
        rec_bad = self.analyzer.get_recovery_recommendation(analysis_bad)
        
        self.assertIn('Highly recommended', rec_good)
        self.assertIn('Recommended', rec_moderate)
        self.assertIn('Not recommended', rec_bad)


if __name__ == '__main__':
    unittest.main()
