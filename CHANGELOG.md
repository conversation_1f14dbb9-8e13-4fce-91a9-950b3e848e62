# Changelog

All notable changes to the Excel File Recovery Software will be documented in this file.

## [1.0.0] - 2024-12-21

### Added
- Initial release of Excel File Recovery Software
- GUI-based interface using tkinter
- File discovery module to scan common Excel temporary locations
- File analysis module to validate and assess recoverability
- Recovery functionality with multiple naming options
- Advanced filtering and search capabilities
- Support for multiple Excel formats (xlsx, xls, xlsm, xlsb)
- Detection of temporary files and auto-recovery files
- Batch recovery capabilities
- User-friendly progress tracking
- Comprehensive test suite
- Detailed user documentation

### Features
- Automatic scanning of standard Excel temporary locations:
  - `%LOCALAPPDATA%\Microsoft\Office\UnsavedFiles`
  - `%APPDATA%\Microsoft\Excel`
  - `%TEMP%` directory
  - Documents folder
- Custom location scanning
- File filtering by type and modification date
- Real-time search functionality
- File analysis with corruption level assessment
- Recovery recommendations based on analysis
- Multiple file naming options during recovery
- Overwrite protection with automatic renaming
- Progress tracking during operations
- Detailed file information display

### Technical Details
- Built with Python 3.7+ compatibility
- Uses tkinter for cross-platform GUI
- Implements ZIP-based analysis for XLSX files
- OLE2 format detection for legacy XLS files
- Comprehensive error handling and logging
- Modular architecture for easy maintenance
- Full test coverage with unittest framework

### Supported File Types
- Excel Workbooks (.xlsx, .xlsm)
- Excel 97-2003 Workbooks (.xls)
- Excel Binary Workbooks (.xlsb)
- Excel Templates (.xltx, .xltm)
- Auto-recovery files (.asd)
- Excel temporary files (.tmp, .xlk)

### System Requirements
- Windows 7 or later (primary support)
- Python 3.7 or later
- 50MB available disk space
- Excel 2010 or later (for auto-recovery files)
