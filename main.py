#!/usr/bin/env python3
"""
Excel File Recovery Software
Main entry point for the application
"""

import sys
import os

# Add src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from gui.main_window import ExcelRecoveryApp

def main():
    """Main entry point"""
    try:
        print("Starting Excel File Recovery Tool...")
        app = ExcelRecoveryApp()
        print("Application initialized successfully")
        app.run()
    except Exception as e:
        print(f"Error starting application: {e}")
        import traceback
        traceback.print_exc()
        input("Press Enter to exit...")

if __name__ == "__main__":
    main()
