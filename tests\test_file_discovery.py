"""
Test cases for file discovery module
"""

import unittest
import tempfile
import os
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from core.file_discovery import ExcelFileDiscovery


class TestFileDiscovery(unittest.TestCase):
    """Test cases for ExcelFileDiscovery class"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.discovery = ExcelFileDiscovery()
        self.temp_dir = tempfile.mkdtemp()
        
        # Create test files
        self.test_files = [
            'test.xlsx',
            'document.xls',
            'workbook.xlsm',
            '~$temp.xlsx',
            'Excel_backup.tmp',
            'autorecovery.asd',
            'regular.txt'  # Non-Excel file
        ]
        
        for filename in self.test_files:
            file_path = os.path.join(self.temp_dir, filename)
            with open(file_path, 'w') as f:
                f.write('test content')
    
    def tearDown(self):
        """Clean up test fixtures"""
        import shutil
        shutil.rmtree(self.temp_dir)
    
    def test_get_standard_locations(self):
        """Test getting standard Excel file locations"""
        locations = self.discovery.get_standard_locations()
        self.assertIsInstance(locations, list)
        self.assertGreater(len(locations), 0)
        
        # Check that locations are strings and exist or are valid paths
        for location in locations:
            self.assertIsInstance(location, str)
    
    def test_scan_location(self):
        """Test scanning a specific location"""
        files = self.discovery.scan_location(self.temp_dir)
        
        # Should find Excel-related files but not regular.txt
        excel_files = [f for f in files if f['name'] in self.test_files[:-1]]  # Exclude regular.txt
        self.assertGreater(len(excel_files), 0)
        
        # Check file info structure
        for file_info in files:
            self.assertIn('path', file_info)
            self.assertIn('name', file_info)
            self.assertIn('size', file_info)
            self.assertIn('modified', file_info)
            self.assertIn('created', file_info)
            self.assertIn('extension', file_info)
            self.assertIn('is_temp', file_info)
            self.assertIn('is_auto_recovery', file_info)
    
    def test_is_temp_file(self):
        """Test temporary file detection"""
        self.assertTrue(self.discovery._is_temp_file('~$temp.xlsx'))
        self.assertTrue(self.discovery._is_temp_file('Excel_backup.tmp'))
        self.assertFalse(self.discovery._is_temp_file('regular.xlsx'))
    
    def test_is_auto_recovery_file(self):
        """Test auto-recovery file detection"""
        self.assertTrue(self.discovery._is_auto_recovery_file('autorecovery.asd'))
        self.assertTrue(self.discovery._is_auto_recovery_file('AutoRecovery save of Document1.asd'))
        self.assertFalse(self.discovery._is_auto_recovery_file('regular.xlsx'))
    
    def test_discover_all_files(self):
        """Test discovering all files"""
        files = self.discovery.discover_all_files([self.temp_dir])
        
        # Should find files from both standard locations and custom location
        self.assertIsInstance(files, list)
        
        # Check for duplicates (should be removed)
        paths = [f['path'] for f in files]
        self.assertEqual(len(paths), len(set(paths)))
    
    def test_nonexistent_location(self):
        """Test scanning non-existent location"""
        files = self.discovery.scan_location('/nonexistent/path')
        self.assertEqual(len(files), 0)


if __name__ == '__main__':
    unittest.main()
